use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, <PERSON>q, <PERSON>h, <PERSON><PERSON><PERSON>, Deserialize)]
pub enum TileType {
    Character(u8), // 万子
    Circle(u8),    // 筒子
    Bamboo(u8),    // 条子
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, <PERSON>q, <PERSON>h, Serialize, Deserialize)]
pub struct Tile {
    pub tile_type: TileType,
}

impl Tile {
    pub fn new(tile_type: TileType) -> Self {
        Self { tile_type }
    }

    pub fn to_string(&self) -> String {
        match self.tile_type {
            TileType::Character(n) => format!("{}万", n),
            TileType::Circle(n) => format!("{}筒", n),
            TileType::Bamboo(n) => format!("{}条", n),
        }
    }
}

use crate::game::rules::Meld;
use crate::game::tile::Tile;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct Player {
    pub id: String,
    pub name: String,
    pub hand: Vec<Tile>,
    pub discard_pile: Vec<Tile>,
    pub melds: Vec<Meld>,
    pub score: i32,
    pub dealer: bool,   // 是否是庄家
    pub win_count: i32, // 连庄次数
    pub ready: bool,    // 是否听牌
}

impl Player {
    pub fn new(id: String, name: String) -> Self {
        Self {
            id,
            name,
            hand: Vec::new(),
            discard_pile: Vec::new(),
            melds: Vec::new(),
            score: 0,
            dealer: false,
            win_count: 0,
            ready: false,
        }
    }

    pub fn draw_tile(&mut self, tile: Tile) {
        self.hand.push(tile);
        self.sort_hand();
    }

    pub fn discard_tile(&mut self, tile: Tile) -> Option<Tile> {
        if let Some(pos) = self.hand.iter().position(|t| *t == tile) {
            let tile = self.hand.remove(pos);
            self.discard_pile.push(tile);
            self.ready = false; // 打牌后需要重新判定听牌
            Some(tile)
        } else {
            None
        }
    }

    // 手牌排序：万筒条的顺序，同类型按数字大小排序
    pub fn sort_hand(&mut self) {
        self.hand.sort_by(|a, b| {
            use crate::game::tile::TileType::*;
            match (a.tile_type, b.tile_type) {
                (Character(n1), Character(n2)) => n1.cmp(&n2),
                (Circle(n1), Circle(n2)) => n1.cmp(&n2),
                (Bamboo(n1), Bamboo(n2)) => n1.cmp(&n2),
                (Character(_), _) => std::cmp::Ordering::Less,
                (Circle(_), Character(_)) => std::cmp::Ordering::Greater,
                (Circle(_), _) => std::cmp::Ordering::Less,
                (Bamboo(_), _) => std::cmp::Ordering::Greater,
            }
        });
    }

    // 获取所有暗杠
    pub fn get_concealed_kongs(&self) -> Vec<Vec<Tile>> {
        let mut result = Vec::new();
        let mut counts = std::collections::HashMap::new();

        // 统计手牌中每种牌的数量
        for tile in &self.hand {
            *counts.entry(tile).or_insert(0) += 1;
        }

        // 找出数量为4的牌
        for (tile, &count) in counts.iter() {
            if count == 4 {
                result.push(vec![**tile; 4]);
            }
        }

        result
    }

    // 获取所有明杠（已经碰过的牌）
    pub fn get_exposed_kongs(&self, new_tile: Tile) -> Vec<Vec<Tile>> {
        let mut result = Vec::new();

        for meld in &self.melds {
            if let Meld {
                meld_type: crate::game::rules::MeldType::Peng,
                tiles,
                ..
            } = meld
            {
                if tiles[0] == new_tile {
                    let mut kong = tiles.clone();
                    kong.push(new_tile);
                    result.push(kong);
                }
            }
        }

        result
    }

    // 检查是否可以胡牌
    pub fn can_win(&self, new_tile: Option<Tile>) -> Option<crate::game::win_check::WinPattern> {
        let mut hand = self.hand.clone();
        if let Some(tile) = new_tile {
            hand.push(tile);
        }

        // 获取所有明牌（吃、碰、杠的牌）
        let mut meld_tiles = Vec::new();
        for meld in &self.melds {
            meld_tiles.extend_from_slice(&meld.tiles);
        }

        crate::game::win_check::check_win(&hand, &meld_tiles)
    }

    // 计算番数
    pub fn calculate_points(&self, pattern: &crate::game::win_check::WinPattern) -> i32 {
        use crate::game::win_check::WinPattern::*;
        match pattern {
            PingHu => 1,     // 平胡1番
            DaDui => 2,      // 大对子（碰碰胡）2番
            QiDui => 4,      // 七对4番
            QingYiSe => 4,   // 清一色4番
            QingPeng => 8,   // 清一色碰碰胡8番
            QingQiDui => 16, // 清一色七对16番
            LongQiDui => 16, // 龙七对16番
        }
    }
}
